# LAN to VM Access Configuration Guide

## Current Firewall Configuration Analysis

### ✅ **Already Configured Rules**

The `setup-vm-networking.sh` script **already includes** the necessary firewall rules for LAN-to-VM access:

```bash
# Allow inter-network communication for management access
iptables -A FORWARD -s *************/24 -d ************/24 -j ACCEPT
iptables -A FORWARD -s ************/24 -d *************/24 -j ACCEPT
```

### **What These Rules Enable:**
- **LAN → VM**: Devices on *************/24 can initiate connections to VMs on ************/24
- **VM → LAN**: VMs can respond back to LAN devices and initiate connections to LAN
- **Bidirectional**: Full communication in both directions

## ✅ **No Additional iptables Rules Needed**

The current configuration **already allows** direct access from LAN devices to VM services. No additional firewall rules are required on the Proxmox host.

## Testing LAN-to-VM Connectivity

### **Test Scenario:**
- LAN device: `**************`
- VM: `*************`
- Service: SSH (port 22)

### **Step 1: Basic Connectivity Test**

From any LAN device (192.168.111.x):

```bash
# Test basic connectivity
ping *************

# Test specific port (SSH)
telnet ************* 22

# Or using nmap
nmap -p 22 *************

# Test HTTP service
curl http://*************:80
```

### **Step 2: SSH Access Test**

```bash
# SSH from LAN device to VM
ssh username@*************

# SSH with specific key
ssh -i ~/.ssh/vm_key username@*************
```

### **Step 3: Web Service Access**

```bash
# Access web service from LAN device
curl http://*************:80
curl https://*************:443

# Or open in browser
# http://*************
```

## VM Internal Configuration Requirements

### **VM Firewall Configuration**

The VM's internal firewall (if enabled) must allow the desired services:

#### **Ubuntu/Debian VMs:**
```bash
# Allow SSH from LAN network
sudo ufw allow from *************/24 to any port 22

# Allow HTTP/HTTPS from LAN network
sudo ufw allow from *************/24 to any port 80
sudo ufw allow from *************/24 to any port 443

# Or allow all traffic from LAN network
sudo ufw allow from *************/24

# Check status
sudo ufw status
```

#### **CentOS/RHEL VMs:**
```bash
# Allow SSH from LAN network
sudo firewall-cmd --permanent --add-rich-rule='rule family="ipv4" source address="*************/24" port protocol="tcp" port="22" accept'

# Allow HTTP/HTTPS from LAN network
sudo firewall-cmd --permanent --add-rich-rule='rule family="ipv4" source address="*************/24" port protocol="tcp" port="80" accept'
sudo firewall-cmd --permanent --add-rich-rule='rule family="ipv4" source address="*************/24" port protocol="tcp" port="443" accept'

# Reload firewall
sudo firewall-cmd --reload

# Check status
sudo firewall-cmd --list-all
```

#### **Windows VMs:**
```powershell
# Allow inbound connections from LAN network
New-NetFirewallRule -DisplayName "Allow LAN SSH" -Direction Inbound -Protocol TCP -LocalPort 22 -RemoteAddress *************/24 -Action Allow
New-NetFirewallRule -DisplayName "Allow LAN HTTP" -Direction Inbound -Protocol TCP -LocalPort 80 -RemoteAddress *************/24 -Action Allow
New-NetFirewallRule -DisplayName "Allow LAN HTTPS" -Direction Inbound -Protocol TCP -LocalPort 443 -RemoteAddress *************/24 -Action Allow
```

## Service-Specific Configuration Examples

### **SSH Server (Port 22)**
```bash
# VM configuration - ensure SSH allows connections
sudo systemctl enable ssh
sudo systemctl start ssh

# Edit SSH config if needed
sudo nano /etc/ssh/sshd_config
# Ensure: PermitRootLogin yes/no (as desired)
# Ensure: PasswordAuthentication yes/no (as desired)
```

### **Web Server (Ports 80/443)**
```bash
# Apache
sudo systemctl enable apache2
sudo systemctl start apache2

# Nginx
sudo systemctl enable nginx
sudo systemctl start nginx

# Ensure web server binds to all interfaces
# Check configuration files for Listen directives
```

### **Database Services**
```bash
# MySQL/MariaDB (Port 3306)
# Edit /etc/mysql/mysql.conf.d/mysqld.cnf
bind-address = 0.0.0.0  # Allow external connections

# PostgreSQL (Port 5432)
# Edit /etc/postgresql/*/main/postgresql.conf
listen_addresses = '*'
# Edit /etc/postgresql/*/main/pg_hba.conf
host all all *************/24 md5
```

## Advanced Configuration Options

### **Port Forwarding (If Needed)**

If you want to expose VM services on the host's IP, add DNAT rules:

```bash
# Forward host port 8080 to VM port 80
sudo iptables -t nat -A PREROUTING -p tcp --dport 8080 -j DNAT --to-destination *************:80
sudo iptables -A FORWARD -p tcp -d ************* --dport 80 -j ACCEPT

# Forward host port 2222 to VM SSH port 22
sudo iptables -t nat -A PREROUTING -p tcp --dport 2222 -j DNAT --to-destination *************:22
sudo iptables -A FORWARD -p tcp -d ************* --dport 22 -j ACCEPT
```

### **Load Balancing Multiple VMs**

```bash
# Round-robin between multiple web servers
sudo iptables -t nat -A PREROUTING -p tcp --dport 80 -m statistic --mode nth --every 2 --packet 0 -j DNAT --to-destination *************:80
sudo iptables -t nat -A PREROUTING -p tcp --dport 80 -j DNAT --to-destination *************:80
```

## Troubleshooting Common Issues

### **Connection Refused**
1. **Check VM firewall**: Ensure service ports are open
2. **Check service status**: Ensure service is running on VM
3. **Check binding**: Ensure service binds to 0.0.0.0, not just 127.0.0.1

### **Connection Timeout**
1. **Check host firewall**: Verify FORWARD rules are active
2. **Check routing**: Ensure packets can reach VM subnet
3. **Check VM network**: Ensure VM has correct IP and gateway

### **Partial Connectivity**
1. **Check VM default gateway**: Should be ************
2. **Check DNS**: VMs should use ************ or public DNS
3. **Check return path**: Ensure responses can reach LAN devices

## Verification Commands

### **On Proxmox Host:**
```bash
# Check firewall rules
sudo iptables -L FORWARD -n -v

# Check routing
ip route show

# Check bridge status
ip addr show vmbr0
ip addr show vmbr1

# Test connectivity to VM
ping *************
```

### **On LAN Device:**
```bash
# Test routing to VM subnet
traceroute *************

# Test specific services
nmap -p 22,80,443 *************

# Test from specific source
ping -I ************** *************
```

### **On VM:**
```bash
# Check network configuration
ip addr show
ip route show

# Check listening services
sudo netstat -tlnp
sudo ss -tlnp

# Check firewall status
sudo ufw status  # Ubuntu/Debian
sudo firewall-cmd --list-all  # CentOS/RHEL
```

## Security Considerations

### **Recommended Security Practices:**
1. **VM Firewalls**: Always enable and configure VM firewalls
2. **Service Hardening**: Secure services (SSH keys, strong passwords)
3. **Network Segmentation**: Use specific rules instead of blanket access
4. **Monitoring**: Monitor access logs and network traffic
5. **Updates**: Keep VMs and services updated

### **Restrictive Access Example:**
```bash
# On VM: Allow only specific LAN IPs for SSH
sudo ufw allow from ************** to any port 22
sudo ufw allow from ************** to any port 22

# Deny all other SSH access
sudo ufw deny 22
```

## Summary

✅ **Current Configuration**: Already supports full LAN-to-VM access
✅ **No Additional Rules Needed**: Existing firewall rules are sufficient
✅ **VM Configuration**: Focus on VM internal firewall and service configuration
✅ **Testing**: Use ping, telnet, nmap, and service-specific tools
✅ **Security**: Implement VM-level security controls as needed
