# Proxmox VM Networking with Webmin DHCP Management

## Overview

This configuration provides a complete Proxmox VM networking solution with the following features:

- **Network Isolation**: VMs run on isolated ************/24 subnet
- **Internet Access**: Full NAT connectivity hiding host MAC address
- **LAN Accessibility**: VMs accessible from main *************/24 network
- **NetworkManager Compatibility**: Host networking managed by GNOME System Settings
- **Webmin Integration**: DHCP server fully manageable through Webmin web interface

## Network Architecture

```
Internet
    ↓
Router (*************)
    ↓
Host Physical Interface (enp2s0)
    ↓
┌─────────────────────────────────────────────────────────────┐
│                    Proxmox Host                             │
│                                                             │
│  vmbr0 (Host Bridge)          vmbr1 (VM Bridge)            │
│  *************00/24           ************/24              │
│  ↑                            ↑                            │
│  │                            │                            │
│  Host Management              VM Network                    │
│  NetworkManager Managed       ISC DHCP + NAT               │
│                                                             │
│                               VMs: *************-254       │
└─────────────────────────────────────────────────────────────┘
```

## Installation

1. **Run the setup script:**
   ```bash
   ./setup-vm-networking.sh
   ```

2. **Reboot the system:**
   ```bash
   sudo reboot
   ```

3. **Verify configuration:**
   ```bash
   ./verify-webmin-dhcp.sh
   ```

## Network Bridges

### vmbr0 (Host Bridge)
- **Purpose**: Host system connectivity
- **Network**: *************/24
- **Management**: NetworkManager (GNOME System Settings)
- **DHCP**: Provided by router (*************)

### vmbr1 (VM Bridge)
- **Purpose**: Virtual machine connectivity
- **Network**: ************/24
- **Management**: ISC DHCP Server (Webmin compatible)
- **Gateway**: ************ (host)
- **DHCP Range**: *************-254

## DHCP Server Configuration

### ISC DHCP Server (Webmin Compatible)

**Configuration File**: `/etc/dhcp/dhcpd.conf`
**Interface Configuration**: `/etc/default/isc-dhcp-server`
**Log File**: `/var/log/dhcp.log`

### Key Settings:
- **Subnet**: ************/24
- **Range**: *************-254
- **Gateway**: ************
- **DNS**: ************, *******, *******
- **Domain**: vm.local
- **Lease Time**: 12 hours (default), 24 hours (max)

## Webmin Management

### Accessing Webmin DHCP Module

1. **Open Webmin**: `https://[HOST-IP]:10000`
2. **Navigate to**: Servers > DHCP Server
3. **Manage**: VM subnet (************/24)

### Common Tasks in Webmin:

#### Add Static IP Reservation:
1. Click on VM subnet (************/24)
2. Click "Add a new host"
3. Enter MAC address and desired IP
4. Apply configuration

#### Modify DHCP Range:
1. Click on VM subnet (************/24)
2. Click "Edit Range"
3. Modify start/end addresses
4. Apply configuration

#### View Active Leases:
1. Go to "Active Leases" tab
2. View current VM IP assignments

#### Monitor DHCP Logs:
1. Go to "DHCP Server Logs"
2. View lease assignments and renewals

## VM Configuration

### Creating VMs with Isolated Network:

1. **In Proxmox Web Interface:**
   - Create new VM
   - In Network tab, select Bridge: `vmbr1`
   - Model: VirtIO (recommended)

2. **VM will automatically receive:**
   - IP address from *************-254 range
   - Gateway: ************
   - DNS: ************, *******, *******

### VM Connectivity:

- **Internet Access**: ✓ Full access via NAT
- **Host Access**: ✓ Can reach *************00
- **LAN Access**: ✓ Accessible from *************/24 devices
- **VM-to-VM**: ✓ Direct communication on ************/24
- **MAC Privacy**: ✓ Host MAC hidden from internet

## Firewall Rules

### Automatic iptables Rules:
```bash
# NAT for internet access
iptables -t nat -A POSTROUTING -s ************/24 -o vmbr0 -j MASQUERADE

# Forward VM traffic to internet
iptables -A FORWARD -i vmbr1 -o vmbr0 -j ACCEPT
iptables -A FORWARD -i vmbr0 -o vmbr1 -m state --state RELATED,ESTABLISHED -j ACCEPT

# Allow LAN to VM communication
iptables -A FORWARD -s *************/24 -d ************/24 -j ACCEPT
iptables -A FORWARD -s ************/24 -d *************/24 -j ACCEPT
```

## Troubleshooting

### DHCP Server Issues:

```bash
# Check DHCP server status
sudo systemctl status isc-dhcp-server

# Check DHCP configuration syntax
sudo dhcpd -t -cf /etc/dhcp/dhcpd.conf

# View DHCP logs
sudo tail -f /var/log/dhcp.log

# Restart DHCP server
sudo systemctl restart isc-dhcp-server
```

### Network Connectivity Issues:

```bash
# Check bridge interfaces
ip addr show vmbr0
ip addr show vmbr1

# Check routing
ip route show

# Check NAT rules
sudo iptables -t nat -L POSTROUTING

# Check forward rules
sudo iptables -L FORWARD
```

### NetworkManager Issues:

```bash
# Check NetworkManager status
sudo systemctl status NetworkManager

# Check device management
nmcli device status

# Restart NetworkManager
sudo systemctl restart NetworkManager
```

## File Locations

### Configuration Files:
- **Network Interfaces**: `/etc/network/interfaces`
- **DHCP Server Config**: `/etc/dhcp/dhcpd.conf`
- **DHCP Server Defaults**: `/etc/default/isc-dhcp-server`
- **NetworkManager Config**: `/etc/NetworkManager/NetworkManager.conf`
- **IP Forwarding**: `/etc/sysctl.d/99-ip-forward.conf`

### Log Files:
- **DHCP Logs**: `/var/log/dhcp.log`
- **NetworkManager Logs**: `journalctl -u NetworkManager`
- **System Logs**: `/var/log/syslog`

### Service Files:
- **VM Network iptables**: `/etc/systemd/system/vm-network-iptables.service`

## Security Considerations

1. **Network Isolation**: VMs are isolated from host network by default
2. **Controlled Access**: Only specific ports/services accessible from LAN
3. **NAT Protection**: Host MAC address hidden from internet
4. **Firewall Rules**: Explicit allow rules for inter-network communication

## Maintenance

### Regular Tasks:
- Monitor DHCP lease usage through Webmin
- Review DHCP logs for unusual activity
- Update static reservations as needed
- Backup DHCP configuration before major changes

### Backup Important Files:
```bash
sudo cp /etc/dhcp/dhcpd.conf /etc/dhcp/dhcpd.conf.backup
sudo cp /etc/network/interfaces /etc/network/interfaces.backup
```
