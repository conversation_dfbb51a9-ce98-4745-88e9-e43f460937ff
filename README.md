# GitLab CE LXC Container for Proxmox VE

This repository contains comprehensive guides and scripts for setting up GitLab Community Edition in a Proxmox VE LXC container.

## Files Included

- **`gitlab-lxc-setup-guide.md`** - Complete step-by-step setup guide
- **`gitlab-lxc-quick-setup.sh`** - Automated setup script for quick deployment
- **`README.md`** - This file

## Quick Start

### Option 1: Automated Setup (Recommended for Testing)

1. Copy the script to your Proxmox host
2. Edit the configuration variables in `gitlab-lxc-quick-setup.sh`
3. Run the script as root:
   ```bash
   ./gitlab-lxc-quick-setup.sh
   ```

### Option 2: Manual Setup (Recommended for Production)

Follow the detailed guide in `gitlab-lxc-setup-guide.md` for complete control over the installation process.

## Container Specifications

- **Base**: Debian 12 (Bookworm) LXC template
- **Type**: Unprivileged container (recommended for security)
- **Resources**:
  - RAM: 8GB (minimum 4GB)
  - CPU: 4 cores (minimum 2 cores)
  - Storage: 50GB (minimum 20GB)
- **Network**: Configurable (DHCP or static IP)

## Key Features

✅ **Security Hardened**
- Unprivileged container configuration
- Fail2ban integration
- SSL/TLS encryption
- Security best practices

✅ **Production Ready**
- Automated backups
- Performance optimization
- Resource monitoring
- Log management

✅ **Proxmox Integrated**
- Proper mount points for data persistence
- Backup integration
- Firewall configuration
- Resource optimization

✅ **Easy Maintenance**
- Automated health checks
- Update procedures
- Troubleshooting guides
- Maintenance scripts

## Prerequisites

- Proxmox VE 7.x or 8.x
- Sufficient host resources (minimum 6GB RAM, 4 CPU cores available)
- Network configuration planned
- Basic knowledge of Linux administration

## Post-Installation

After installation, you'll have:

1. **GitLab CE** accessible via web interface
2. **Initial admin account** with generated password
3. **SSL/TLS encryption** (self-signed by default)
4. **Automated backups** configured
5. **Security hardening** applied
6. **Performance optimization** for container environment

## Next Steps

1. Access GitLab web interface
2. Change default admin password
3. Configure additional users and projects
4. Set up CI/CD pipelines
5. Configure external integrations

## Support

For detailed instructions, troubleshooting, and advanced configuration, refer to:
- `gitlab-lxc-setup-guide.md` - Complete documentation
- [GitLab Official Documentation](https://docs.gitlab.com/)
- [Proxmox VE Documentation](https://pve.proxmox.com/pve-docs/)

## License

This guide is provided as-is for educational and production use. Please ensure compliance with GitLab CE licensing terms.