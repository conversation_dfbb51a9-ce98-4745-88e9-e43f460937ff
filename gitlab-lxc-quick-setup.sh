#!/bin/bash
# GitLab CE LXC Container Quick Setup Script for Proxmox VE
# Run this script on your Proxmox host

set -e

# Configuration variables - MODIFY THESE
CONTAINER_ID=200
HOSTNAME="gitlab-ce"
MEMORY=8192
SWAP=2048
CORES=4
DISK_SIZE=50
STORAGE="local-lvm"
TEMPLATE_STORAGE="local"
NETWORK_BRIDGE="vmbr0"
IP_CONFIG="dhcp"  # Change to "***********00/24,gw=***********" for static IP
ROOT_PASSWORD=""  # Leave empty to be prompted

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}GitLab CE LXC Container Setup Script${NC}"
echo "======================================"

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   echo -e "${RED}This script must be run as root${NC}"
   exit 1
fi

# Check if container ID already exists
if pct status $CONTAINER_ID >/dev/null 2>&1; then
    echo -e "${RED}Container ID $CONTAINER_ID already exists!${NC}"
    exit 1
fi

# Download Debian 12 template if not available
echo -e "${YELLOW}Checking for Debian 12 template...${NC}"
if ! pveam list $TEMPLATE_STORAGE | grep -q "debian-12-standard"; then
    echo -e "${YELLOW}Downloading Debian 12 template...${NC}"
    pveam update
    pveam download $TEMPLATE_STORAGE debian-12-standard_12.2-1_amd64.tar.zst
fi

# Get root password if not set
if [ -z "$ROOT_PASSWORD" ]; then
    echo -e "${YELLOW}Enter root password for the container:${NC}"
    read -s ROOT_PASSWORD
fi

# Create data directories on host
echo -e "${YELLOW}Creating data directories...${NC}"
mkdir -p /mnt/gitlab-data/{config,logs,data}
chown -R 100000:100000 /mnt/gitlab-data

# Create the container
echo -e "${YELLOW}Creating LXC container...${NC}"
pct create $CONTAINER_ID $TEMPLATE_STORAGE:vztmpl/debian-12-standard_12.2-1_amd64.tar.zst \
  --hostname $HOSTNAME \
  --memory $MEMORY \
  --swap $SWAP \
  --cores $CORES \
  --rootfs $STORAGE:$DISK_SIZE \
  --net0 name=eth0,bridge=$NETWORK_BRIDGE,ip=$IP_CONFIG,firewall=1 \
  --unprivileged 1 \
  --features nesting=1 \
  --mp0 /mnt/gitlab-data,mp=/opt/gitlab-data \
  --start 0

# Set root password
echo -e "${YELLOW}Setting root password...${NC}"
echo "root:$ROOT_PASSWORD" | pct exec $CONTAINER_ID -- chpasswd

# Start the container
echo -e "${YELLOW}Starting container...${NC}"
pct start $CONTAINER_ID

# Wait for container to be ready
echo -e "${YELLOW}Waiting for container to be ready...${NC}"
sleep 10

# Update system and install dependencies
echo -e "${YELLOW}Updating system and installing dependencies...${NC}"
pct exec $CONTAINER_ID -- bash -c "
    apt update && apt upgrade -y
    apt install -y curl wget gnupg2 software-properties-common apt-transport-https ca-certificates
    apt install -y curl openssh-server ca-certificates tzdata perl postfix
"

# Add GitLab repository and install
echo -e "${YELLOW}Installing GitLab CE...${NC}"
pct exec $CONTAINER_ID -- bash -c "
    curl -fsSL https://packages.gitlab.com/gitlab/gitlab-ce/gpgkey | gpg --dearmor -o /usr/share/keyrings/gitlab.gpg
    echo 'deb [signed-by=/usr/share/keyrings/gitlab.gpg] https://packages.gitlab.com/gitlab/gitlab-ce/debian/ bookworm main' > /etc/apt/sources.list.d/gitlab_gitlab-ce.list
    apt update
    EXTERNAL_URL='https://$HOSTNAME' apt install -y gitlab-ce
"

# Get container IP
CONTAINER_IP=$(pct exec $CONTAINER_ID -- hostname -I | awk '{print $1}')

echo -e "${GREEN}GitLab CE LXC Container Setup Complete!${NC}"
echo "=========================================="
echo -e "Container ID: ${GREEN}$CONTAINER_ID${NC}"
echo -e "Hostname: ${GREEN}$HOSTNAME${NC}"
echo -e "IP Address: ${GREEN}$CONTAINER_IP${NC}"
echo -e "Resources: ${GREEN}${CORES} CPU cores, ${MEMORY}MB RAM, ${DISK_SIZE}GB storage${NC}"
echo ""
echo -e "${YELLOW}Next Steps:${NC}"
echo "1. Get the initial root password:"
echo "   pct exec $CONTAINER_ID -- cat /etc/gitlab/initial_root_password"
echo ""
echo "2. Access GitLab web interface:"
echo "   https://$CONTAINER_IP (accept self-signed certificate)"
echo ""
echo "3. Login with username 'root' and the password from step 1"
echo ""
echo "4. Follow the complete setup guide in gitlab-lxc-setup-guide.md for:"
echo "   - SSL certificate configuration"
echo "   - Security hardening"
echo "   - Backup setup"
echo "   - Performance optimization"
echo ""
echo -e "${GREEN}Setup completed successfully!${NC}"