# GitLab CE LXC Container Setup Guide for Proxmox VE (Homelab Edition)

This guide provides step-by-step instructions for creating and configuring a Debian 12-based LXC container to host GitLab Community Edition in Proxmox Virtual Environment, optimized for homelab use.

## Important LXC Considerations for GitLab CE

**Key Compatibility Notes:**
- GitLab CE works well in LXC containers but requires specific configurations
- **Privileged containers are recommended** for GitLab CE to avoid systemd and permission issues
- Memory requirements are higher in containers due to isolation overhead
- Some GitLab features may require additional container features enabled

## Prerequisites

- Proxmox VE 7.x or 8.x installed and configured
- Debian 12 (Bookworm) LXC template downloaded
- Sufficient host resources (minimum 8GB RAM, 4 CPU cores available)
- Network configuration planned (static IP or DHCP)

## Part 1: Proxmox LXC Container Creation

### 1.1 Download Debian 12 Template

First, ensure you have the Debian 12 template available:

```bash
# On Proxmox host, download the template if not already available
pveam update
pveam available | grep debian-12
pveam download local debian-12-standard_12.2-1_amd64.tar.zst
```

### 1.2 Container Configuration Recommendations

**Resource Requirements for GitLab CE in LXC:**
- **RAM**: Minimum 6GB, Recommended 8-12GB (higher than VM due to container overhead)
- **CPU**: Minimum 4 cores, Recommended 6 cores
- **Storage**: Minimum 30GB, Recommended 50GB+
- **Container Type**: **Privileged** (recommended for GitLab CE compatibility)

**Why Privileged Container for GitLab CE:**
- Avoids systemd and permission issues common with GitLab in unprivileged containers
- Better compatibility with GitLab's omnibus package
- Reduces complex troubleshooting of container-specific issues
- Homelab environment has lower security requirements than production

**Known LXC Issues with GitLab CE:**
- Unprivileged containers may have systemd service startup issues
- PostgreSQL and Redis may have permission problems in unprivileged containers
- GitLab's omnibus installer expects full system access
- Some GitLab features (like container registry) work better in privileged containers

### 1.3 Create the Container via Web UI

1. **Access Proxmox Web Interface**
   - Navigate to your Proxmox VE web interface
   - Select your node in the left panel

2. **Create CT (Container)**
   - Click "Create CT" button
   - Fill in the following configuration:

**General Tab:**
- **CT ID**: Choose available ID (e.g., 200)
- **Hostname**: `gitlab` (or `gitlab.moc` if you have local DNS)
- **Unprivileged container**: ❌ **Unchecked** (use privileged for GitLab CE)
- **Resource Pool**: (optional)
- **Password**: Set root password
- **SSH public key**: (optional for homelab)

**Template Tab:**
- **Storage**: local
- **Template**: debian-12-standard_12.2-1_amd64.tar.zst

**Root Disk Tab:**
- **Storage**: Choose appropriate storage (local-lvm recommended)
- **Disk size (GB)**: 50
- **Mount options**: (leave default)

**CPU Tab:**
- **Cores**: 4
- **CPU limit**: (leave default)
- **CPU units**: 1024

**Memory Tab:**
- **Memory (MB)**: 10240 (10GB - increased for LXC overhead)
- **Swap (MB)**: 2048

**Network Tab:**
- **Bridge**: vmbr0 (or your configured bridge)
- **VLAN Tag**: (if using VLANs)
- **Firewall**: ✓ **Checked** (recommended)
- **IPv4**: Static or DHCP based on your network setup
  - If Static: Set IP/CIDR, Gateway
  - If DHCP: Leave IP field empty
- **IPv6**: Configure if needed

**DNS Tab:**
- **DNS domain**: moc (or your local domain)
- **DNS servers**: Your DNS servers (e.g., *******, *******)

**Confirm Tab:**
- **Start after created**: ✓ **Checked**

3. **Click "Create"** to create the container

### 1.4 Alternative: Create Container via CLI

If you prefer command line, use this example:

```bash
# Create privileged container for GitLab CE
pct create 200 local:vztmpl/debian-12-standard_12.2-1_amd64.tar.zst \
  --hostname gitlab \
  --memory 10240 \
  --swap 2048 \
  --cores 6 \
  --rootfs local-lvm:50 \
  --net0 name=eth0,bridge=vmbr0,ip=dhcp,firewall=1 \
  --unprivileged 0 \
  --features nesting=1,keyctl=1 \
  --start 1

# Set root password
pct set 200 --password
```

**Important CLI Notes:**
- `--unprivileged 0` creates a privileged container
- `--features nesting=1,keyctl=1` enables features needed for GitLab
- Increased memory to 10GB for better LXC performance

### 1.5 Container Post-Creation Configuration

After container creation, configure additional settings:

```bash
# Enable additional features for GitLab CE
pct set 200 --features nesting=1,keyctl=1

# Configure memory and CPU if needed
pct set 200 --memory 10240 --swap 2048 --cores 6

# Add mount point for GitLab data persistence (recommended for homelab)
# This creates a bind mount from host to container for easy backups
mkdir -p /mnt/gitlab-data
pct set 200 --mp0 /mnt/gitlab-data,mp=/opt/gitlab-data
```

**LXC Features Explained:**
- `nesting=1`: Allows nested containers (useful for GitLab CI runners)
- `keyctl=1`: Enables keyctl syscall (needed for some GitLab services)

## Part 2: Initial Container Setup

### 2.1 Access the Container

```bash
# Enter container console
pct enter 200

# Or SSH if you configured SSH keys
ssh root@<container-ip>
```

### 2.2 Update System

```bash
# Update package lists and system
apt update && apt upgrade -y

# Install essential packages
apt install -y curl wget gnupg2 software-properties-common apt-transport-https ca-certificates
```

### 2.3 Configure Timezone and Locale

```bash
# Set timezone
timedatectl set-timezone America/New_York  # Adjust to your timezone
# Or use: dpkg-reconfigure tzdata

# Configure locale
locale-gen en_US.UTF-8
update-locale LANG=en_US.UTF-8
```

### 2.4 Configure Network (if using static IP)

If you need to configure static networking manually:

```bash
# Edit network configuration
nano /etc/systemd/network/eth0.network

# Example static configuration:
[Match]
Name=eth0

[Network]
DHCP=no
Address=*************/24
Gateway=***********
DNS=*******
DNS=*******

# Restart networking
systemctl restart systemd-networkd
```

## Part 3: GitLab CE Installation

### 3.1 Install Dependencies

```bash
# Install required dependencies
apt install -y curl openssh-server ca-certificates tzdata perl postfix

# During postfix installation, choose "Internet Site" and set your domain
# If you don't have a domain, you can choose "Local only"
```

### 3.2 Add GitLab Repository

```bash
# Add GitLab official repository
curl -fsSL https://packages.gitlab.com/gitlab/gitlab-ce/gpgkey | gpg --dearmor -o /usr/share/keyrings/gitlab.gpg

echo "deb [signed-by=/usr/share/keyrings/gitlab.gpg] https://packages.gitlab.com/gitlab/gitlab-ce/debian/ bookworm main" > /etc/apt/sources.list.d/gitlab_gitlab-ce.list

# Update package list
apt update
```

### 3.3 Install GitLab CE

```bash
# Set external URL for homelab (HTTP for simplicity)
export EXTERNAL_URL="http://gitlab.moc"
# Or use IP if you don't have local DNS: export EXTERNAL_URL="http://*************"

# Install GitLab CE
apt install -y gitlab-ce

# The installation will automatically run gitlab-ctl reconfigure
# This process takes 5-10 minutes and may appear to hang - be patient!
```

**Installation Notes:**
- Using HTTP for homelab simplicity (no SSL certificate management)
- The installation process is resource-intensive and may take 10-15 minutes in LXC
- You may see warnings about swap - these are normal in containers

**Note**: The installation process will take several minutes and will automatically configure GitLab.

### 3.4 Verify Installation

```bash
# Check GitLab status
gitlab-ctl status

# Check if all services are running
gitlab-ctl service-list

# View GitLab logs if needed
gitlab-ctl tail
```

## Part 4: Basic GitLab Configuration (Homelab)

### 4.1 Verify GitLab is Running

```bash
# Check all GitLab services are running
gitlab-ctl status

# If any services are down, restart them
gitlab-ctl restart

# Check GitLab health
gitlab-rake gitlab:check
```

### 4.2 Configure GitLab for Homelab Use

```bash
# Edit GitLab configuration for homelab optimization
nano /etc/gitlab/gitlab.rb

# Add these configurations for better LXC performance:
# Reduce memory usage
postgresql['shared_buffers'] = "256MB"
postgresql['effective_cache_size'] = "1GB"

# Optimize for smaller environment
puma['worker_processes'] = 2
puma['min_threads'] = 4
puma['max_threads'] = 4

# Reduce Sidekiq concurrency
sidekiq['max_concurrency'] = 10

# Disable features not needed in homelab
gitlab_rails['gitlab_signup_enabled'] = false
gitlab_rails['gitlab_default_projects_features_issues'] = true
gitlab_rails['gitlab_default_projects_features_merge_requests'] = true
gitlab_rails['gitlab_default_projects_features_wiki'] = false
gitlab_rails['gitlab_default_projects_features_snippets'] = false

# Apply configuration
gitlab-ctl reconfigure
```

## Part 5: Initial GitLab Configuration

### 5.1 Get Initial Root Password

```bash
# Get the initial root password
cat /etc/gitlab/initial_root_password

# Note: This file is automatically deleted after 24 hours
# Copy the password immediately after installation
```

### 5.2 Access GitLab Web Interface

1. Open web browser and navigate to your GitLab URL
2. Login with:
   - Username: `root`
   - Password: (from the file above)

### 5.3 Initial Configuration Steps

1. **Change Root Password**
   - Go to User Settings → Password
   - Set a strong new password

2. **Configure Admin Area Settings**
   - Go to Admin Area → Settings → General
   - Configure visibility and access controls
   - Set up sign-up restrictions

3. **Configure Email Settings** (optional but recommended)
   ```bash
   # Edit GitLab configuration
   nano /etc/gitlab/gitlab.rb

   # Add email configuration (example for Gmail)
   gitlab_rails['smtp_enable'] = true
   gitlab_rails['smtp_address'] = "smtp.gmail.com"
   gitlab_rails['smtp_port'] = 587
   gitlab_rails['smtp_user_name'] = "<EMAIL>"
   gitlab_rails['smtp_password'] = "your-app-password"
   gitlab_rails['smtp_domain'] = "smtp.gmail.com"
   gitlab_rails['smtp_authentication'] = "login"
   gitlab_rails['smtp_enable_starttls_auto'] = true
   gitlab_rails['smtp_tls'] = false
   gitlab_rails['gitlab_email_from'] = '<EMAIL>'

   # Reconfigure
   gitlab-ctl reconfigure

   # Test email configuration
   gitlab-rails console -e production
   # In the console, run:
   # Notify.test_email('<EMAIL>', 'Test Subject', 'Test Body').deliver_now
   ```

## Part 6: Proxmox Integration and Backup Strategy

### 6.1 Configure Data Persistence (Optional)

For easier data management and backups, you can use mount points:

```bash
# On Proxmox host, create directory for GitLab data
mkdir -p /mnt/gitlab-data

# Stop the container
pct stop 200

# Add mount point to container
pct set 200 --mp0 /mnt/gitlab-data,mp=/opt/gitlab-data

# Start the container
pct start 200
```

**Note:** For homelab use, the default container storage is usually sufficient. Mount points are optional but helpful for:
- Easier access to GitLab data from the host
- Separate backup strategies
- Moving data between containers

### 6.2 Proxmox Backup Strategy (Recommended for Homelab)

For homelab use, rely on Proxmox's built-in backup system:

**Option 1: Web UI Backup Configuration**
1. Go to Datacenter → Backup in Proxmox web interface
2. Click "Add" to create a backup job
3. Configure:
   - **Node**: Your Proxmox node
   - **Storage**: Your backup storage (local, NFS, etc.)
   - **Schedule**: Daily at 2 AM (or your preference)
   - **Selection Mode**: Include selected VMs
   - **VM ID**: 200 (your GitLab container)
   - **Compression**: LZO or ZSTD
   - **Mode**: Snapshot (for consistency)

**Option 2: Manual Backup**
```bash
# On Proxmox host, create manual backup
vzdump 200 --mode snapshot --compress lzo --storage local

# List existing backups
ls -la /var/lib/vz/dump/
```

**GitLab Application Backup (Optional)**
```bash
# Inside container, create GitLab application backup
gitlab-backup create

# Backups are stored in /var/opt/gitlab/backups/
# These are included in Proxmox container backups automatically
```

### 6.5 Network Configuration for External Access

Configure firewall rules for GitLab access:

```bash
# On Proxmox host, configure firewall rules
# Edit container firewall rules
nano /etc/pve/firewall/200.fw

# Add these rules:
[RULES]
IN ACCEPT -p tcp --dport 22 -source ***********/24  # SSH access
IN ACCEPT -p tcp --dport 80                          # HTTP
IN ACCEPT -p tcp --dport 443                         # HTTPS
IN ACCEPT -p tcp --dport 2222                        # GitLab SSH (if configured)

# Enable firewall for container
[OPTIONS]
enable: 1
```

### 6.6 Resource Monitoring and Optimization

Monitor container resource usage:

```bash
# Check container resource usage
pct config 200
pct status 200

# Monitor real-time usage
pct exec 200 -- htop
pct exec 200 -- free -h
pct exec 200 -- df -h

# Adjust resources if needed
pct set 200 --memory 12288  # Increase to 12GB if needed
pct set 200 --cores 6       # Increase CPU cores if needed
```

### 6.7 Container Optimization for GitLab

```bash
# Inside container, optimize GitLab settings
nano /etc/gitlab/gitlab.rb

# Add performance optimizations
postgresql['shared_buffers'] = "256MB"
postgresql['max_connections'] = 200
postgresql['work_mem'] = "16MB"

# Optimize Puma (GitLab web server)
puma['worker_processes'] = 4
puma['min_threads'] = 4
puma['max_threads'] = 4

# Optimize Sidekiq (background jobs)
sidekiq['max_concurrency'] = 25

# Reconfigure after changes
gitlab-ctl reconfigure
```

## Part 7: LXC-Specific Troubleshooting and Common Issues

### 7.1 Common LXC + GitLab Issues and Solutions

**Issue: GitLab services fail to start**
```bash
# Check systemd status in container
systemctl status gitlab-runsvdir

# If systemd issues, try restarting the container
# On Proxmox host:
pct restart 200

# Inside container, manually start GitLab
gitlab-ctl start
```

**Issue: PostgreSQL fails to start**
```bash
# Check PostgreSQL logs
gitlab-ctl tail postgresql

# Common fix: Restart PostgreSQL service
gitlab-ctl restart postgresql

# If persistent issues, reconfigure GitLab
gitlab-ctl reconfigure
```

**Issue: High memory usage or OOM kills**
```bash
# Check memory usage
free -h
gitlab-ctl status

# On Proxmox host, increase container memory
pct set 200 --memory 12288  # Increase to 12GB

# Inside container, reduce GitLab worker processes
nano /etc/gitlab/gitlab.rb
# Add:
puma['worker_processes'] = 1
sidekiq['max_concurrency'] = 5

gitlab-ctl reconfigure
```

**Issue: Container won't start after reboot**
```bash
# On Proxmox host, check container status
pct status 200

# Start container manually
pct start 200

# Check for errors
pct enter 200
journalctl -xe
```

### 7.3 Service Management and Monitoring

```bash
# Enable GitLab services to start on boot
systemctl enable gitlab-runsvdir

# Create GitLab monitoring script
cat > /usr/local/bin/gitlab-health-check.sh << 'EOF'
#!/bin/bash
# GitLab health check script

# Check GitLab status
if ! gitlab-ctl status > /dev/null 2>&1; then
    echo "$(date): GitLab services are not running properly" >> /var/log/gitlab-health.log
    gitlab-ctl restart
fi

# Check disk space
DISK_USAGE=$(df /var/opt/gitlab | tail -1 | awk '{print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "$(date): Disk usage is at ${DISK_USAGE}%" >> /var/log/gitlab-health.log
fi

# Check memory usage
MEM_USAGE=$(free | grep Mem | awk '{printf "%.0f", $3/$2 * 100.0}')
if [ $MEM_USAGE -gt 90 ]; then
    echo "$(date): Memory usage is at ${MEM_USAGE}%" >> /var/log/gitlab-health.log
fi
EOF

chmod +x /usr/local/bin/gitlab-health-check.sh

# Add to crontab for regular health checks
crontab -e
# Add this line:
# */5 * * * * /usr/local/bin/gitlab-health-check.sh
```

### 7.4 Log Management

```bash
# Configure log rotation
cat > /etc/logrotate.d/gitlab-custom << 'EOF'
/var/log/gitlab-health.log
/var/log/gitlab-backup.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    copytruncate
}
EOF

# Set up centralized logging (optional)
# Install rsyslog if not present
apt install -y rsyslog

# Configure GitLab to use syslog
nano /etc/gitlab/gitlab.rb
# Add:
logging['svlogd_size'] = 200 * 1024 * 1024  # 200 MB
logging['svlogd_num'] = 30
logging['svlogd_timeout'] = 24 * 60 * 60    # 24 hours

gitlab-ctl reconfigure
```

### 7.5 Final GitLab Admin Configuration

```bash
# Access GitLab Rails console for advanced configuration
gitlab-rails console -e production

# In the console, you can run:
# Disable new user registrations
ApplicationSetting.current.update!(signup_enabled: false)

# Set default project visibility to private
ApplicationSetting.current.update!(default_project_visibility: 0)

# Configure password complexity
ApplicationSetting.current.update!(password_authentication_enabled_for_web: true)
ApplicationSetting.current.update!(password_authentication_enabled_for_git: true)

# Exit console
exit
```

### 7.6 Performance Tuning

```bash
# Optimize GitLab for container environment
nano /etc/gitlab/gitlab.rb

# Reduce memory usage for smaller environments
postgresql['shared_buffers'] = "128MB"
postgresql['effective_cache_size'] = "512MB"

# Optimize Gitaly (Git repository storage)
gitaly['ruby_max_rss'] = 300000000  # 300MB
gitaly['concurrency'] = [
  {
    'rpc' => "/gitaly.SmartHTTPService/PostReceivePack",
    'max_per_repo' => 3
  },
  {
    'rpc' => "/gitaly.SSHService/SSHUploadPack",
    'max_per_repo' => 3
  }
]

# Optimize Redis
redis['maxmemory'] = "256mb"
redis['maxmemory_policy'] = "allkeys-lru"

# Reconfigure
gitlab-ctl reconfigure
```

## Part 8: Troubleshooting and Maintenance

### 8.1 Common Issues and Solutions

**Issue: GitLab not accessible after installation**
```bash
# Check GitLab status
gitlab-ctl status

# Check logs
gitlab-ctl tail

# Restart all services
gitlab-ctl restart

# Reconfigure GitLab
gitlab-ctl reconfigure
```

**Issue: High memory usage**
```bash
# Check memory usage
free -h
gitlab-ctl status

# Reduce worker processes
nano /etc/gitlab/gitlab.rb
# Reduce puma['worker_processes'] and sidekiq['max_concurrency']

gitlab-ctl reconfigure
```

**Issue: SSL certificate problems**
```bash
# Check certificate status
openssl x509 -in /etc/gitlab/ssl/gitlab.yourdomain.com.crt -text -noout

# Renew Let's Encrypt certificate
gitlab-ctl renew-le-certs

# Check GitLab SSL configuration
gitlab-ctl show-config | grep ssl
```

### 8.2 Regular Maintenance Tasks

```bash
# Weekly maintenance script
cat > /usr/local/bin/gitlab-maintenance.sh << 'EOF'
#!/bin/bash
# GitLab weekly maintenance

echo "$(date): Starting GitLab maintenance" >> /var/log/gitlab-maintenance.log

# Update system packages
apt update && apt upgrade -y

# Clean up old logs
find /var/log/gitlab -name "*.log.*" -mtime +30 -delete

# Optimize GitLab database
gitlab-rake db:migrate
gitlab-rake cache:clear

# Check GitLab configuration
gitlab-ctl check-config

echo "$(date): GitLab maintenance completed" >> /var/log/gitlab-maintenance.log
EOF

chmod +x /usr/local/bin/gitlab-maintenance.sh

# Schedule weekly maintenance
crontab -e
# Add:
# 0 3 * * 0 /usr/local/bin/gitlab-maintenance.sh
```

### 8.3 Backup and Restore Procedures

**Create Manual Backup:**
```bash
# Create backup
gitlab-backup create

# Backup configuration files
tar -czf /var/opt/gitlab/backups/gitlab-config-$(date +%Y%m%d).tar.gz /etc/gitlab/
```

**Restore from Backup:**
```bash
# Stop GitLab services
gitlab-ctl stop unicorn
gitlab-ctl stop puma
gitlab-ctl stop sidekiq

# Restore backup (replace TIMESTAMP with actual timestamp)
gitlab-backup restore BACKUP=TIMESTAMP

# Restore configuration
tar -xzf /var/opt/gitlab/backups/gitlab-config-YYYYMMDD.tar.gz -C /

# Restart GitLab
gitlab-ctl restart
gitlab-rake gitlab:check SANITIZE=true
```

## Conclusion

You now have a fully configured GitLab CE instance running in a Proxmox LXC container with:

- ✅ Secure unprivileged container configuration
- ✅ Proper resource allocation and optimization
- ✅ SSL/TLS encryption
- ✅ Automated backups and monitoring
- ✅ Security hardening
- ✅ Integration with Proxmox VE

**Next Steps:**
1. Create your first project in GitLab
2. Set up additional users and groups
3. Configure CI/CD pipelines
4. Set up GitLab Runner for automated builds
5. Configure integrations with external services

**Important Notes:**
- Keep your GitLab instance updated regularly
- Monitor resource usage and adjust as needed
- Regularly test your backup and restore procedures
- Review security logs periodically

For additional help, consult the [GitLab documentation](https://docs.gitlab.com/) and [Proxmox VE documentation](https://pve.proxmox.com/pve-docs/).

---

## Step-by-Step Installation Walkthrough

Let's walk through the installation process step by step. Follow along and let me know if you encounter any issues.

### Step 1: Download Debian 12 Template

First, let's ensure you have the Debian 12 template:

```bash
# On your Proxmox host, run:
pveam update
pveam available | grep debian-12
pveam download local debian-12-standard_12.2-1_amd64.tar.zst
```

**Expected output:** You should see the template downloading. This may take a few minutes.

### Step 2: Create the LXC Container

Use the Proxmox web interface or CLI to create the container with these specifications:

**Web UI Method:**
- Container ID: 200 (or your choice)
- Hostname: `gitlab` or `gitlab.moc`
- **Important:** Uncheck "Unprivileged container" (use privileged)
- Memory: 10GB (10240 MB)
- CPU: 6 cores
- Storage: 50GB
- Network: DHCP or static IP

**CLI Method:**
```bash
pct create 200 local:vztmpl/debian-12-standard_12.2-1_amd64.tar.zst \
  --hostname gitlab \
  --memory 10240 \
  --swap 2048 \
  --cores 6 \
  --rootfs local-lvm:50 \
  --net0 name=eth0,bridge=vmbr0,ip=dhcp,firewall=1 \
  --unprivileged 0 \
  --features nesting=1,keyctl=1 \
  --start 1
```

**Checkpoint:** Container should be created and started. Check with `pct status 200`

### Step 3: Initial Container Setup

Enter the container and update the system:

```bash
# Enter container
pct enter 200

# Update system
apt update && apt upgrade -y

# Install dependencies
apt install -y curl wget gnupg2 software-properties-common apt-transport-https ca-certificates
```

**Checkpoint:** System should update without errors. This may take 5-10 minutes.

### Step 4: Install GitLab Dependencies

```bash
# Install required packages
apt install -y curl openssh-server ca-certificates tzdata perl postfix

# During postfix installation:
# - Choose "Internet Site"
# - For mail name, use your hostname (gitlab.moc) or leave default
```

**Checkpoint:** All packages should install successfully.

### Step 5: Add GitLab Repository

```bash
# Add GitLab official repository
curl -fsSL https://packages.gitlab.com/gitlab/gitlab-ce/gpgkey | gpg --dearmor -o /usr/share/keyrings/gitlab.gpg

echo "deb [signed-by=/usr/share/keyrings/gitlab.gpg] https://packages.gitlab.com/gitlab/gitlab-ce/debian/ bookworm main" > /etc/apt/sources.list.d/gitlab_gitlab-ce.list

# Update package list
apt update
```

**Checkpoint:** You should see GitLab packages available when running `apt update`

### Step 6: Install GitLab CE

This is the critical step. The installation may take 10-15 minutes and appear to hang:

```bash
# Set external URL for homelab
export EXTERNAL_URL="http://gitlab.moc"
# Or use your container's IP: export EXTERNAL_URL="http://192.168.1.xxx"

# Install GitLab CE (this will take time!)
apt install -y gitlab-ce
```

**What to expect:**
- Installation will download ~500MB of packages
- Configuration will run automatically (gitlab-ctl reconfigure)
- Process may appear to hang for 5-10 minutes - this is normal
- You may see warnings about swap - ignore these in LXC

**Checkpoint:** Installation should complete with "gitlab Reconfigured!" message

### Step 7: Verify Installation

```bash
# Check GitLab status
gitlab-ctl status

# All services should show "run" status
# If any show "down", run: gitlab-ctl restart
```

### Step 8: Get Initial Password and Access GitLab

```bash
# Get the initial root password
cat /etc/gitlab/initial_root_password

# Note this password - the file is deleted after 24 hours!
```

Now open a web browser and navigate to:
- `http://gitlab.moc` (if you have local DNS)
- `http://[container-ip]` (use the container's IP address)

Login with:
- Username: `root`
- Password: (from the file above)

**Checkpoint:** You should see the GitLab welcome screen

### Step 9: Basic Configuration

Once logged in:
1. Change the root password (User Settings → Password)
2. Create your first project or user account
3. Explore the GitLab interface

### Step 10: Optional Optimizations

If you experience performance issues:

```bash
# Edit GitLab configuration
nano /etc/gitlab/gitlab.rb

# Add these lines for better LXC performance:
puma['worker_processes'] = 2
sidekiq['max_concurrency'] = 10
postgresql['shared_buffers'] = "256MB"

# Apply changes
gitlab-ctl reconfigure
```

---

**Ready to start? Let me know when you're ready to begin, and we'll go through each step together!**