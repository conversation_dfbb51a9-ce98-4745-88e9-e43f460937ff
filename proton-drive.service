[Unit]
Description=Proton Drive (rclone)
AssertPathIsDirectory=/mnt/proton-drive
After=network-online.target

[Service]
Type=simple
User=wm
Group=wm
ExecStart=/usr/bin/rclone mount Proton: /mnt/proton-drive --config=/home/<USER>/.config/rclone/rclone.conf --vfs-cache-mode writes --vfs-cache-max-size 1G --vfs-cache-max-age 1h --log-level INFO
ExecStop=/bin/fusermount -u /mnt/proton-drive
Restart=always
RestartSec=10

[Install]
WantedBy=default.target
