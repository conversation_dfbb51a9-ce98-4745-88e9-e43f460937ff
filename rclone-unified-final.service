[Unit]
Description=Rclone Unified Service (Web UI + Proton Drive Mount)
After=network-online.target
Wants=network-online.target
AssertPathIsDirectory=/mnt/proton-drive

[Service]
Type=forking
User=wm
Group=wm
ExecStartPre=/bin/bash -c 'if mountpoint -q /mnt/proton-drive; then umount -l /mnt/proton-drive; fi'
ExecStart=/bin/bash -c '/usr/bin/rclone rcd --rc-web-gui --rc-addr localhost:5572 --rc-no-auth --rc-serve --config=/home/<USER>/.config/rclone/rclone.conf --daemon && sleep 3 && curl -X POST "http://localhost:5572/mount/mount" -H "Content-Type: application/json" -d "{\"fs\":\"Proton:\", \"mountPoint\":\"/mnt/proton-drive\", \"vfsOpt\":{\"CacheMode\":2, \"CacheMaxSize\":\"1G\", \"CacheMaxAge\":\"1h\"}}"'
ExecStop=/bin/bash -c 'curl -X POST "http://localhost:5572/mount/unmount" -H "Content-Type: application/json" -d "{\"mountPoint\":\"/mnt/proton-drive\"}" || true'
ExecStopPost=/bin/bash -c 'pkill -f "rclone rcd" || true'
Restart=always
RestartSec=10
Environment=HOME=/home/<USER>
PIDFile=/run/user/1000/rclone-unified.pid

[Install]
WantedBy=multi-user.target
