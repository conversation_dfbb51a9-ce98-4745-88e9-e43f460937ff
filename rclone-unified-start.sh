#!/bin/bash

# Rclone Unified Service Startup Script
# Starts rclone daemon with Web UI and creates Proton Drive mount

set -e

# Configuration
RCLONE_CONFIG="/home/<USER>/.config/rclone/rclone.conf"
MOUNT_POINT="/mnt/proton-drive"
RC_ADDR="localhost:5572"

# Function to log messages
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# Clean up any existing mount
if mountpoint -q "$MOUNT_POINT"; then
    log "Unmounting existing mount at $MOUNT_POINT"
    umount -l "$MOUNT_POINT" || true
fi

# Kill any existing rclone rcd processes
pkill -f "rclone rcd" || true
sleep 2

# Start rclone daemon with Web UI
log "Starting rclone daemon with Web UI"
rclone rcd --rc-web-gui --rc-addr "$RC_ADDR" --rc-no-auth --rc-serve --config="$RCLONE_CONFIG" &
RCLONE_PID=$!

# Wait for daemon to be ready
log "Waiting for rclone daemon to be ready..."
for i in {1..30}; do
    if curl -s "http://$RC_ADDR/config/listremotes" -X POST >/dev/null 2>&1; then
        log "Rclone daemon is ready"
        break
    fi
    if [ $i -eq 30 ]; then
        log "ERROR: Rclone daemon failed to start"
        exit 1
    fi
    sleep 1
done

# Create the mount via API
log "Creating Proton Drive mount at $MOUNT_POINT"
MOUNT_RESULT=$(curl -s -X POST "http://$RC_ADDR/mount/mount" \
    -H "Content-Type: application/json" \
    -d "{\"fs\":\"Proton:\", \"mountPoint\":\"$MOUNT_POINT\", \"vfsOpt\":{\"CacheMode\":2, \"CacheMaxSize\":\"1G\", \"CacheMaxAge\":\"1h\"}}")

if echo "$MOUNT_RESULT" | grep -q "error"; then
    log "ERROR: Failed to create mount: $MOUNT_RESULT"
    exit 1
fi

log "Mount created successfully"

# Verify mount is working
if [ -d "$MOUNT_POINT" ] && ls "$MOUNT_POINT" >/dev/null 2>&1; then
    log "Mount verification successful"
else
    log "ERROR: Mount verification failed"
    exit 1
fi

log "Rclone unified service started successfully"
log "Web UI available at: http://$RC_ADDR"
log "Mount point: $MOUNT_POINT"

# Keep the script running (daemon will run in background)
wait $RCLONE_PID
