[Unit]
Description=Rclone Unified Service (Web UI + Proton Drive Mount)
After=network-online.target
Wants=network-online.target
AssertPathIsDirectory=/mnt/proton-drive

[Service]
Type=simple
User=wm
Group=wm
ExecStart=/home/<USER>/PARAvsc/rclone-unified-start.sh
ExecStop=/bin/bash -c 'curl -s -X POST "http://localhost:5572/mount/unmount" -H "Content-Type: application/json" -d "{\"mountPoint\":\"/mnt/proton-drive\"}" || true; pkill -f "rclone rcd" || true'
Restart=always
RestartSec=10
Environment=HOME=/home/<USER>
WorkingDirectory=/home/<USER>/PARAvsc

[Install]
WantedBy=multi-user.target
