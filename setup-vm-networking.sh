#!/bin/bash

# Proxmox VM Networking Setup Script
# Creates isolated VM network with NAT and LAN accessibility

set -e

echo "=== Proxmox VM Networking Configuration ==="
echo "This script will configure:"
echo "- vmbr0: Host bridge (*************/24) - NetworkManager compatible"
echo "- vmbr1: VM bridge (************/24) - Isolated with NAT"
echo ""

# Backup current configuration
echo "1. Backing up current network configuration..."
sudo cp /etc/network/interfaces /etc/network/interfaces.backup.$(date +%Y%m%d_%H%M%S)

# Install required packages
echo "2. Installing required packages..."
sudo apt update
sudo apt install -y isc-dhcp-server bind9-dnsutils iptables-persistent rfkill

# Create new network interfaces configuration
echo "3. Creating new network interfaces configuration..."
sudo tee /etc/network/interfaces > /dev/null << 'INTERFACES_EOF'
# network interface settings; autogenerated
# Please do NOT modify this file directly, unless you know what
# you're doing.
#
# If you want to manage parts of the network configuration manually,
# please utilize the 'source' or 'source-directory' directives to do
# so.
# PVE will preserve these directives, but will NOT read its network
# configuration from sourced files, so do not attempt to move any of
# the PVE managed interfaces into external files!

source /etc/network/interfaces.d/*

auto lo
iface lo inet loopback

# Physical interface - will be managed by NetworkManager
iface enp2s0 inet manual

# Host bridge - for host connectivity (NetworkManager compatible)
auto vmbr0
iface vmbr0 inet dhcp
    bridge-ports enp2s0
    bridge-stp off
    bridge-fd 0
    bridge-vlan-aware yes
    bridge-vids 2-4094

# VM bridge - dedicated for virtual machines with isolated subnet
auto vmbr1
iface vmbr1 inet static
    address ************/24
    bridge-ports none
    bridge-stp off
    bridge-fd 0
    bridge-vlan-aware yes
    bridge-vids 2-4094
    # Enable IP forwarding for this interface
    post-up echo 1 > /proc/sys/net/ipv4/ip_forward
    # Configure NAT for VM internet access (hides host MAC)
    post-up iptables -t nat -A POSTROUTING -s ************/24 -o vmbr0 -j MASQUERADE
    post-up iptables -A FORWARD -i vmbr1 -o vmbr0 -j ACCEPT
    post-up iptables -A FORWARD -i vmbr0 -o vmbr1 -m state --state RELATED,ESTABLISHED -j ACCEPT
    # Allow inter-network communication for management access
    post-up iptables -A FORWARD -s *************/24 -d ************/24 -j ACCEPT
    post-up iptables -A FORWARD -s ************/24 -d *************/24 -j ACCEPT
    # Cleanup rules on interface down
    pre-down iptables -t nat -D POSTROUTING -s ************/24 -o vmbr0 -j MASQUERADE 2>/dev/null || true
    pre-down iptables -D FORWARD -i vmbr1 -o vmbr0 -j ACCEPT 2>/dev/null || true
    pre-down iptables -D FORWARD -i vmbr0 -o vmbr1 -m state --state RELATED,ESTABLISHED -j ACCEPT 2>/dev/null || true
    pre-down iptables -D FORWARD -s *************/24 -d ************/24 -j ACCEPT 2>/dev/null || true
    pre-down iptables -D FORWARD -s ************/24 -d *************/24 -j ACCEPT 2>/dev/null || true
INTERFACES_EOF

# Configure ISC DHCP Server for VM network (Webmin compatible)
echo "4. Configuring ISC DHCP Server for VM network..."

# Configure DHCP server to listen only on VM bridge
sudo tee /etc/default/isc-dhcp-server > /dev/null << 'DHCP_DEFAULT_EOF'
# Defaults for isc-dhcp-server (sourced by /etc/init.d/isc-dhcp-server)

# Path to dhcpd's config file (default: /etc/dhcp/dhcpd.conf).
DHCPDv4_CONF=/etc/dhcp/dhcpd.conf

# Path to dhcpd's PID file (default: /var/run/dhcpd.pid).
DHCPDv4_PID=/var/run/dhcpd.pid

# Additional options to start dhcpd with.
#	Don't use options -cf or -pf here; use DHCPD_CONF/ DHCPD_PID instead
#OPTIONS=""

# On what interfaces should the DHCP server (dhcpd) serve DHCP requests?
#	Separate multiple interfaces with spaces, e.g. "eth0 eth1".
INTERFACESv4="vmbr1"
INTERFACESv6=""
DHCP_DEFAULT_EOF

# Create main DHCP configuration file (Webmin standard location)
sudo tee /etc/dhcp/dhcpd.conf > /dev/null << 'DHCP_CONF_EOF'
# ISC DHCP Server Configuration for Proxmox VM Network
# This file is managed by Webmin and can be edited through the web interface

# Global settings
default-lease-time 600;
max-lease-time 7200;
authoritative;

# Use this to send dhcp log messages to a different log file (you also
# have to hack syslog.conf to complete the redirection).
log-facility local7;

# VM Network Subnet (************/24)
subnet ************ netmask ************* {
    range ************* **************;
    option routers ************;
    option domain-name-servers ************, *******, *******;
    option domain-name "vm.local";
    option broadcast-address **************;
    option subnet-mask *************;

    # Default lease time for this subnet
    default-lease-time 43200;  # 12 hours
    max-lease-time 86400;      # 24 hours
}

# Host Network Subnet Declaration (required but not served)
# This prevents DHCP server from complaining about unknown subnet
subnet ************* netmask ************* {
    # This subnet is not served by this DHCP server
    # It's just declared to prevent warnings
}

# Example static host reservations (uncomment and modify as needed)
# host vm-server-01 {
#     hardware ethernet 52:54:00:12:34:56;
#     fixed-address *************;
#     option host-name "vm-server-01";
# }

# host vm-workstation-01 {
#     hardware ethernet 52:54:00:12:34:57;
#     fixed-address ************1;
#     option host-name "vm-workstation-01";
# }
DHCP_CONF_EOF

# Configure DNS forwarding for VM network
echo "5. Setting up DNS forwarding..."
sudo tee /etc/bind/named.conf.local >> /dev/null << 'DNS_EOF' || true
// VM network DNS forwarding configuration
zone "vm.local" {
    type forward;
    forwarders { *******; *******; };
};
DNS_EOF

# Configure NetworkManager to be compatible
echo "6. Configuring NetworkManager compatibility..."
sudo tee /etc/NetworkManager/NetworkManager.conf > /dev/null << 'NM_EOF'
[main]
plugins=ifupdown,keyfile

[ifupdown]
managed=true

[device]
wifi.scan-rand-mac-address=no

# Ignore VM bridge to prevent conflicts
unmanaged-devices=interface-name:vmbr1
NM_EOF

# Enable IP forwarding permanently
echo "7. Enabling IP forwarding..."
echo 'net.ipv4.ip_forward=1' | sudo tee /etc/sysctl.d/99-ip-forward.conf > /dev/null

# Enable WiFi if disabled
echo "8. Enabling WiFi hardware..."
sudo rfkill unblock wifi || true

# Create systemd service for iptables rules persistence
echo "8. Creating iptables persistence service..."
sudo tee /etc/systemd/system/vm-network-iptables.service > /dev/null << 'SERVICE_EOF'
[Unit]
Description=VM Network iptables rules
After=network.target

[Service]
Type=oneshot
RemainAfterExit=yes
ExecStart=/bin/bash -c 'echo 1 > /proc/sys/net/ipv4/ip_forward'
ExecStart=/sbin/iptables -t nat -A POSTROUTING -s ************/24 -o vmbr0 -j MASQUERADE
ExecStart=/sbin/iptables -A FORWARD -i vmbr1 -o vmbr0 -j ACCEPT
ExecStart=/sbin/iptables -A FORWARD -i vmbr0 -o vmbr1 -m state --state RELATED,ESTABLISHED -j ACCEPT
ExecStart=/sbin/iptables -A FORWARD -s *************/24 -d ************/24 -j ACCEPT
ExecStart=/sbin/iptables -A FORWARD -s ************/24 -d *************/24 -j ACCEPT
ExecStop=/sbin/iptables -t nat -D POSTROUTING -s ************/24 -o vmbr0 -j MASQUERADE
ExecStop=/sbin/iptables -D FORWARD -i vmbr1 -o vmbr0 -j ACCEPT
ExecStop=/sbin/iptables -D FORWARD -i vmbr0 -o vmbr1 -m state --state RELATED,ESTABLISHED -j ACCEPT
ExecStop=/sbin/iptables -D FORWARD -s *************/24 -d ************/24 -j ACCEPT
ExecStop=/sbin/iptables -D FORWARD -s ************/24 -d *************/24 -j ACCEPT

[Install]
WantedBy=multi-user.target
SERVICE_EOF

echo "9. Enabling services..."
sudo systemctl enable isc-dhcp-server
sudo systemctl enable vm-network-iptables

# Configure DHCP server logging for Webmin
echo "10. Configuring DHCP logging for Webmin..."
sudo tee -a /etc/rsyslog.conf > /dev/null << 'RSYSLOG_EOF'

# DHCP Server logging for Webmin
local7.*    /var/log/dhcp.log
RSYSLOG_EOF

# Create log file with proper permissions
sudo touch /var/log/dhcp.log
sudo chown syslog:adm /var/log/dhcp.log
sudo chmod 640 /var/log/dhcp.log

echo ""
echo "=== Configuration Complete ==="
echo ""
echo "IMPORTANT: To apply changes, you need to:"
echo "1. Reboot the system: sudo reboot"
echo "   OR"
echo "2. Restart networking manually:"
echo "   sudo systemctl restart networking"
echo "   sudo systemctl restart NetworkManager"
echo "   sudo systemctl restart rsyslog"
echo "   sudo systemctl start isc-dhcp-server"
echo "   sudo systemctl start vm-network-iptables"
echo ""
echo "After restart, you will have:"
echo "- vmbr0: Host network (*************/24) - NetworkManager managed"
echo "- vmbr1: VM network (************/24) - Isolated with NAT"
echo ""
echo "VM Configuration:"
echo "- Use vmbr1 as the network bridge for VMs"
echo "- VMs will get IPs from *************-254"
echo "- VMs will have internet access via NAT"
echo "- VMs will be accessible from LAN devices"
echo "- Host MAC address will be hidden from internet"
echo ""
echo "Webmin DHCP Management:"
echo "- Access Webmin at https://$(hostname -I | awk '{print $1}'):10000"
echo "- Go to Servers > DHCP Server"
echo "- The VM subnet (************/24) will be visible and editable"
echo "- DHCP configuration file: /etc/dhcp/dhcpd.conf"
echo "- DHCP log file: /var/log/dhcp.log"
echo "- Service name: isc-dhcp-server"
