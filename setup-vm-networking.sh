#!/bin/bash

# Proxmox VM Networking Setup Script
# Creates isolated VM network with NAT and LAN accessibility

set -e

echo "=== Proxmox VM Networking Configuration ==="
echo "This script will configure:"
echo "- vmbr0: Host bridge (*************/24) - NetworkManager compatible"
echo "- vmbr1: VM bridge (************/24) - Isolated with NAT"
echo ""

# Backup current configuration
echo "1. Backing up current network configuration..."
sudo cp /etc/network/interfaces /etc/network/interfaces.backup.$(date +%Y%m%d_%H%M%S)

# Install required packages
echo "2. Installing required packages..."
sudo apt update
sudo apt install -y dnsmasq iptables-persistent rfkill

# Create new network interfaces configuration
echo "3. Creating new network interfaces configuration..."
sudo tee /etc/network/interfaces > /dev/null << 'INTERFACES_EOF'
# network interface settings; autogenerated
# Please do NOT modify this file directly, unless you know what
# you're doing.
#
# If you want to manage parts of the network configuration manually,
# please utilize the 'source' or 'source-directory' directives to do
# so.
# PVE will preserve these directives, but will NOT read its network
# configuration from sourced files, so do not attempt to move any of
# the PVE managed interfaces into external files!

source /etc/network/interfaces.d/*

auto lo
iface lo inet loopback

# Physical interface - will be managed by NetworkManager
iface enp2s0 inet manual

# Host bridge - for host connectivity (NetworkManager compatible)
auto vmbr0
iface vmbr0 inet dhcp
    bridge-ports enp2s0
    bridge-stp off
    bridge-fd 0
    bridge-vlan-aware yes
    bridge-vids 2-4094

# VM bridge - dedicated for virtual machines with isolated subnet
auto vmbr1
iface vmbr1 inet static
    address ************/24
    bridge-ports none
    bridge-stp off
    bridge-fd 0
    bridge-vlan-aware yes
    bridge-vids 2-4094
    # Enable IP forwarding for this interface
    post-up echo 1 > /proc/sys/net/ipv4/ip_forward
    # Configure NAT for VM internet access (hides host MAC)
    post-up iptables -t nat -A POSTROUTING -s ************/24 -o vmbr0 -j MASQUERADE
    post-up iptables -A FORWARD -i vmbr1 -o vmbr0 -j ACCEPT
    post-up iptables -A FORWARD -i vmbr0 -o vmbr1 -m state --state RELATED,ESTABLISHED -j ACCEPT
    # Allow inter-network communication for management access
    post-up iptables -A FORWARD -s *************/24 -d ************/24 -j ACCEPT
    post-up iptables -A FORWARD -s ************/24 -d *************/24 -j ACCEPT
    # Cleanup rules on interface down
    pre-down iptables -t nat -D POSTROUTING -s ************/24 -o vmbr0 -j MASQUERADE 2>/dev/null || true
    pre-down iptables -D FORWARD -i vmbr1 -o vmbr0 -j ACCEPT 2>/dev/null || true
    pre-down iptables -D FORWARD -i vmbr0 -o vmbr1 -m state --state RELATED,ESTABLISHED -j ACCEPT 2>/dev/null || true
    pre-down iptables -D FORWARD -s *************/24 -d ************/24 -j ACCEPT 2>/dev/null || true
    pre-down iptables -D FORWARD -s ************/24 -d *************/24 -j ACCEPT 2>/dev/null || true
INTERFACES_EOF

# Configure dnsmasq for VM DHCP and DNS
echo "4. Configuring DHCP and DNS for VM network..."
sudo tee /etc/dnsmasq.d/vm-network.conf > /dev/null << 'DNSMASQ_EOF'
# VM Network DHCP and DNS Configuration
# Listen only on VM bridge interface
interface=vmbr1
bind-interfaces

# DHCP range for VMs
dhcp-range=*************,**************,*************,12h

# DNS settings
server=*******
server=*******
domain=vm.local

# DHCP options
dhcp-option=option:router,************
dhcp-option=option:dns-server,************
dhcp-option=option:domain-name,vm.local

# Enable DHCP logging
log-dhcp

# Cache size
cache-size=1000
DNSMASQ_EOF

# Configure NetworkManager to be compatible
echo "5. Configuring NetworkManager compatibility..."
sudo tee /etc/NetworkManager/NetworkManager.conf > /dev/null << 'NM_EOF'
[main]
plugins=ifupdown,keyfile

[ifupdown]
managed=true

[device]
wifi.scan-rand-mac-address=no

# Ignore VM bridge to prevent conflicts
unmanaged-devices=interface-name:vmbr1
NM_EOF

# Enable IP forwarding permanently
echo "6. Enabling IP forwarding..."
echo 'net.ipv4.ip_forward=1' | sudo tee /etc/sysctl.d/99-ip-forward.conf > /dev/null

# Enable WiFi if disabled
echo "7. Enabling WiFi hardware..."
sudo rfkill unblock wifi || true

# Create systemd service for iptables rules persistence
echo "8. Creating iptables persistence service..."
sudo tee /etc/systemd/system/vm-network-iptables.service > /dev/null << 'SERVICE_EOF'
[Unit]
Description=VM Network iptables rules
After=network.target

[Service]
Type=oneshot
RemainAfterExit=yes
ExecStart=/bin/bash -c 'echo 1 > /proc/sys/net/ipv4/ip_forward'
ExecStart=/sbin/iptables -t nat -A POSTROUTING -s ************/24 -o vmbr0 -j MASQUERADE
ExecStart=/sbin/iptables -A FORWARD -i vmbr1 -o vmbr0 -j ACCEPT
ExecStart=/sbin/iptables -A FORWARD -i vmbr0 -o vmbr1 -m state --state RELATED,ESTABLISHED -j ACCEPT
ExecStart=/sbin/iptables -A FORWARD -s *************/24 -d ************/24 -j ACCEPT
ExecStart=/sbin/iptables -A FORWARD -s ************/24 -d *************/24 -j ACCEPT
ExecStop=/sbin/iptables -t nat -D POSTROUTING -s ************/24 -o vmbr0 -j MASQUERADE
ExecStop=/sbin/iptables -D FORWARD -i vmbr1 -o vmbr0 -j ACCEPT
ExecStop=/sbin/iptables -D FORWARD -i vmbr0 -o vmbr1 -m state --state RELATED,ESTABLISHED -j ACCEPT
ExecStop=/sbin/iptables -D FORWARD -s *************/24 -d ************/24 -j ACCEPT
ExecStop=/sbin/iptables -D FORWARD -s ************/24 -d *************/24 -j ACCEPT

[Install]
WantedBy=multi-user.target
SERVICE_EOF

echo "9. Enabling services..."
sudo systemctl enable dnsmasq
sudo systemctl enable vm-network-iptables

echo ""
echo "=== Configuration Complete ==="
echo ""
echo "IMPORTANT: To apply changes, you need to:"
echo "1. Reboot the system: sudo reboot"
echo "   OR"
echo "2. Restart networking manually:"
echo "   sudo systemctl restart networking"
echo "   sudo systemctl restart NetworkManager"
echo "   sudo systemctl start dnsmasq"
echo "   sudo systemctl start vm-network-iptables"
echo ""
echo "After restart, you will have:"
echo "- vmbr0: Host network (*************/24) - NetworkManager managed"
echo "- vmbr1: VM network (************/24) - Isolated with NAT"
echo ""
echo "VM Configuration:"
echo "- Use vmbr1 as the network bridge for VMs"
echo "- VMs will get IPs from *************-254"
echo "- VMs will have internet access via NAT"
echo "- VMs will be accessible from LAN devices"
echo "- Host MAC address will be hidden from internet"
