#!/bin/bash

# NetworkManager Integration Test Script
# Tests compatibility between NetworkManager and Proxmox VM networking

echo "=== NetworkManager Integration Test ==="
echo ""

# Test 1: NetworkManager Service Status
echo "1. Testing NetworkManager service..."
if systemctl is-active --quiet NetworkManager; then
    echo "✓ NetworkManager is running"
else
    echo "✗ NetworkManager is NOT running"
    exit 1
fi

# Test 2: NetworkManager Device Management
echo ""
echo "2. Testing NetworkManager device management..."
nmcli device status

echo ""
echo "Checking specific interfaces:"

# Check if enp2s0 is managed
if nmcli device status | grep -q "enp2s0.*connected\|enp2s0.*disconnected"; then
    echo "✓ enp2s0 is managed by NetworkManager"
else
    echo "! enp2s0 may not be fully managed by NetworkManager"
fi

# Check if vmbr0 is properly handled
if nmcli device status | grep -q "vmbr0"; then
    VMBR0_STATUS=$(nmcli device status | grep vmbr0 | awk '{print $3}')
    echo "✓ vmbr0 status: $VMBR0_STATUS"
else
    echo "! vmbr0 not visible to NetworkManager"
fi

# Check if vmbr1 is unmanaged (as configured)
if nmcli device status | grep -q "vmbr1.*unmanaged"; then
    echo "✓ vmbr1 is unmanaged (correct for VM bridge)"
else
    echo "! vmbr1 management status may need verification"
fi

# Test 3: WiFi Functionality
echo ""
echo "3. Testing WiFi functionality..."
if nmcli radio wifi | grep -q "enabled"; then
    echo "✓ WiFi radio is enabled"
    
    # Check if WiFi interface is available
    if nmcli device status | grep -q "wifi"; then
        echo "✓ WiFi interface is available"
        
        # List available networks (if any)
        WIFI_NETWORKS=$(nmcli device wifi list 2>/dev/null | wc -l)
        if [ "$WIFI_NETWORKS" -gt 1 ]; then
            echo "✓ WiFi networks are visible ($((WIFI_NETWORKS-1)) networks found)"
        else
            echo "! No WiFi networks visible (may be normal)"
        fi
    else
        echo "! WiFi interface not available"
    fi
else
    echo "! WiFi radio is disabled"
    echo "  Enable with: sudo rfkill unblock wifi"
fi

# Test 4: Network Connectivity
echo ""
echo "4. Testing network connectivity..."

# Test host connectivity
if ping -c 2 ******* >/dev/null 2>&1; then
    echo "✓ Host has internet connectivity"
else
    echo "✗ Host does NOT have internet connectivity"
fi

# Test DNS resolution
if nslookup google.com >/dev/null 2>&1; then
    echo "✓ DNS resolution is working"
else
    echo "✗ DNS resolution is NOT working"
fi

# Test 5: Bridge Configuration
echo ""
echo "5. Testing bridge configuration..."

# Check vmbr0 configuration
if ip addr show vmbr0 | grep -q "192.168.111"; then
    echo "✓ vmbr0 has correct IP configuration"
else
    echo "✗ vmbr0 does NOT have correct IP configuration"
fi

# Check vmbr1 configuration
if ip addr show vmbr1 | grep -q "************"; then
    echo "✓ vmbr1 has correct IP configuration"
else
    echo "✗ vmbr1 does NOT have correct IP configuration"
fi

# Test 6: GNOME System Settings Compatibility
echo ""
echo "6. Testing GNOME System Settings compatibility..."

# Check if NetworkManager connections are visible
NM_CONNECTIONS=$(nmcli connection show | wc -l)
if [ "$NM_CONNECTIONS" -gt 1 ]; then
    echo "✓ NetworkManager connections are available ($((NM_CONNECTIONS-1)) connections)"
    echo "  Available connections:"
    nmcli connection show | tail -n +2 | sed 's/^/    /'
else
    echo "! No NetworkManager connections found"
fi

# Test 7: Service Conflicts
echo ""
echo "7. Checking for service conflicts..."

# Check if multiple DHCP services are running
DHCP_SERVICES=0
if systemctl is-active --quiet isc-dhcp-server; then
    echo "✓ ISC DHCP Server is running (for VM network)"
    DHCP_SERVICES=$((DHCP_SERVICES + 1))
fi

if systemctl is-active --quiet dnsmasq; then
    echo "! dnsmasq is also running (potential conflict)"
    DHCP_SERVICES=$((DHCP_SERVICES + 1))
fi

if systemctl is-active --quiet dhcpcd; then
    echo "! dhcpcd is also running (potential conflict)"
    DHCP_SERVICES=$((DHCP_SERVICES + 1))
fi

if [ "$DHCP_SERVICES" -eq 1 ]; then
    echo "✓ Only one DHCP service is running (good)"
elif [ "$DHCP_SERVICES" -gt 1 ]; then
    echo "! Multiple DHCP services detected (may cause conflicts)"
else
    echo "! No DHCP services detected"
fi

# Test 8: Routing Table
echo ""
echo "8. Testing routing configuration..."
echo "Current routing table:"
ip route show | sed 's/^/  /'

# Check default route
if ip route show | grep -q "default"; then
    echo "✓ Default route is configured"
else
    echo "✗ Default route is NOT configured"
fi

# Test 9: IP Forwarding
echo ""
echo "9. Testing IP forwarding..."
if [ "$(cat /proc/sys/net/ipv4/ip_forward)" = "1" ]; then
    echo "✓ IP forwarding is enabled"
else
    echo "✗ IP forwarding is NOT enabled"
fi

# Test 10: Firewall Rules
echo ""
echo "10. Testing firewall rules..."

# Check NAT rules
if iptables -t nat -L POSTROUTING | grep -q "************/24"; then
    echo "✓ NAT rules for VM network are active"
else
    echo "✗ NAT rules for VM network are NOT active"
fi

# Check forward rules
if iptables -L FORWARD | grep -q "************/24"; then
    echo "✓ Forward rules for VM network are active"
else
    echo "✗ Forward rules for VM network are NOT active"
fi

echo ""
echo "=== Integration Test Summary ==="
echo ""
echo "If all tests show ✓ (checkmarks), your system is properly configured for:"
echo "- NetworkManager management of host networking"
echo "- GNOME System Settings network control"
echo "- Isolated VM networking with internet access"
echo "- Webmin DHCP management"
echo ""
echo "To access network settings:"
echo "- GNOME: Settings > Network"
echo "- Webmin: https://$(hostname -I | awk '{print $1}'):10000 > Servers > DHCP Server"
echo ""
echo "Next steps:"
echo "1. Create a test VM using vmbr1 bridge"
echo "2. Verify VM gets IP from *************-254 range"
echo "3. Test VM internet connectivity"
echo "4. Test VM accessibility from LAN devices"
