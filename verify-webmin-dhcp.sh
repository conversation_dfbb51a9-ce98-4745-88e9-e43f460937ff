#!/bin/bash

# Webmin DHCP Server Verification Script
# Verifies that ISC DHCP Server is properly configured for Webmin management

echo "=== Webmin DHCP Server Verification ==="
echo ""

# Check if ISC DHCP Server is installed
echo "1. Checking ISC DHCP Server installation..."
if dpkg -l | grep -q isc-dhcp-server; then
    echo "✓ ISC DHCP Server is installed"
else
    echo "✗ ISC DHCP Server is NOT installed"
    exit 1
fi

# Check if Webmin is running
echo ""
echo "2. Checking Webmin status..."
if systemctl is-active --quiet webmin; then
    echo "✓ Webmin is running"
    WEBMIN_PORT=$(grep "^port=" /etc/webmin/miniserv.conf 2>/dev/null | cut -d= -f2 || echo "10000")
    HOST_IP=$(hostname -I | awk '{print $1}')
    echo "  Access URL: https://${HOST_IP}:${WEBMIN_PORT}"
else
    echo "✗ Webmin is NOT running"
    echo "  Install with: sudo apt install webmin"
fi

# Check DHCP configuration file
echo ""
echo "3. Checking DHCP configuration..."
if [ -f /etc/dhcp/dhcpd.conf ]; then
    echo "✓ DHCP configuration file exists: /etc/dhcp/dhcpd.conf"
    
    # Check for VM subnet configuration
    if grep -q "subnet ************" /etc/dhcp/dhcpd.conf; then
        echo "✓ VM subnet (************/24) is configured"
    else
        echo "✗ VM subnet is NOT configured"
    fi
    
    # Check for range configuration
    if grep -q "range *************" /etc/dhcp/dhcpd.conf; then
        echo "✓ DHCP range is configured"
    else
        echo "✗ DHCP range is NOT configured"
    fi
else
    echo "✗ DHCP configuration file does NOT exist"
fi

# Check DHCP server interface configuration
echo ""
echo "4. Checking DHCP server interface configuration..."
if [ -f /etc/default/isc-dhcp-server ]; then
    echo "✓ DHCP server defaults file exists"
    
    if grep -q "INTERFACESv4.*vmbr1" /etc/default/isc-dhcp-server; then
        echo "✓ DHCP server is configured to listen on vmbr1"
    else
        echo "✗ DHCP server is NOT configured for vmbr1"
    fi
else
    echo "✗ DHCP server defaults file does NOT exist"
fi

# Check DHCP service status
echo ""
echo "5. Checking DHCP service status..."
if systemctl is-enabled --quiet isc-dhcp-server; then
    echo "✓ DHCP server is enabled"
else
    echo "✗ DHCP server is NOT enabled"
fi

if systemctl is-active --quiet isc-dhcp-server; then
    echo "✓ DHCP server is running"
else
    echo "✗ DHCP server is NOT running"
    echo "  Check logs: sudo journalctl -u isc-dhcp-server"
fi

# Check network interfaces
echo ""
echo "6. Checking network interfaces..."
if ip addr show vmbr1 >/dev/null 2>&1; then
    echo "✓ vmbr1 interface exists"
    
    if ip addr show vmbr1 | grep -q "************"; then
        echo "✓ vmbr1 has correct IP address (************)"
    else
        echo "✗ vmbr1 does NOT have correct IP address"
    fi
else
    echo "✗ vmbr1 interface does NOT exist"
fi

# Check DHCP log file
echo ""
echo "7. Checking DHCP logging..."
if [ -f /var/log/dhcp.log ]; then
    echo "✓ DHCP log file exists: /var/log/dhcp.log"
    
    # Check recent DHCP activity
    if [ -s /var/log/dhcp.log ]; then
        echo "✓ DHCP log file has content"
        echo "  Recent entries:"
        tail -3 /var/log/dhcp.log | sed 's/^/    /'
    else
        echo "! DHCP log file is empty (normal if no DHCP activity yet)"
    fi
else
    echo "✗ DHCP log file does NOT exist"
fi

# Check iptables rules
echo ""
echo "8. Checking NAT and firewall rules..."
if iptables -t nat -L POSTROUTING | grep -q "************/24"; then
    echo "✓ NAT rule for VM network is active"
else
    echo "✗ NAT rule for VM network is NOT active"
fi

if iptables -L FORWARD | grep -q "************/24"; then
    echo "✓ Forward rules for VM network are active"
else
    echo "✗ Forward rules for VM network are NOT active"
fi

# Test DHCP server configuration
echo ""
echo "9. Testing DHCP configuration syntax..."
if sudo dhcpd -t -cf /etc/dhcp/dhcpd.conf >/dev/null 2>&1; then
    echo "✓ DHCP configuration syntax is valid"
else
    echo "✗ DHCP configuration has syntax errors"
    echo "  Check with: sudo dhcpd -t -cf /etc/dhcp/dhcpd.conf"
fi

echo ""
echo "=== Webmin Access Instructions ==="
echo ""
echo "To manage DHCP through Webmin:"
echo "1. Open web browser and go to: https://${HOST_IP}:${WEBMIN_PORT}"
echo "2. Log in with your system credentials"
echo "3. Navigate to: Servers > DHCP Server"
echo "4. You should see the VM subnet (************/24) listed"
echo "5. Click on the subnet to modify settings"
echo ""
echo "Common Webmin DHCP tasks:"
echo "- Add static IP reservations for specific VMs"
echo "- Modify DHCP range (currently *************-254)"
echo "- Change lease times"
echo "- View active leases"
echo "- Monitor DHCP logs"
echo ""
echo "Configuration files (editable through Webmin):"
echo "- Main config: /etc/dhcp/dhcpd.conf"
echo "- Interface config: /etc/default/isc-dhcp-server"
echo "- Log file: /var/log/dhcp.log"
